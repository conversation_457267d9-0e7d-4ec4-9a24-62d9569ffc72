#!/usr/bin/env python3
"""
Test script to verify ACCOMPLISH plugin fixes
"""

import sys
import os
import json

# Add the plugin path to sys.path
plugin_path = os.path.join(os.path.dirname(__file__), 'services', 'capabilitiesmanager', 'src', 'plugins', 'ACCOMPLISH')
sys.path.insert(0, plugin_path)

def test_brain_interface_json_parsing():
    """Test the Brain interface JSON parsing improvements"""
    print("Testing Brain Interface JSON parsing (simulated)...")

    # Simulate the enhanced JSON parsing logic
    test_cases = [
        '```json\n{"type": "PLAN", "plan": []}\n```',
        '```\n{"type": "PLAN", "plan": []}\n```',
        '{"type": "PLAN", "plan": [],}',  # trailing comma
        '{"type": "PLAN", plan: []}',     # unquoted key
        '// comment\n{"type": "PLAN", "plan": []}',  # with comment
    ]

    for i, test_case in enumerate(test_cases):
        # Simulate the enhanced cleaning logic
        cleaned = test_case.strip()

        # Remove markdown blocks
        if '```' in cleaned:
            import re
            patterns = [
                r'```json\s*(.*?)\s*```',
                r'```\s*(.*?)\s*```',
            ]
            for pattern in patterns:
                match = re.search(pattern, cleaned, re.DOTALL)
                if match:
                    cleaned = match[1].strip()
                    break

        # Apply common fixes
        import re
        cleaned = re.sub(r',\s*([\]\}])', r'\1', cleaned)  # trailing commas
        cleaned = re.sub(r'\/\/.*$', '', cleaned, flags=re.MULTILINE)  # comments
        cleaned = re.sub(r'(?<!")(\b[a-zA-Z_][a-zA-Z0-9_]*\b)(?=\s*:)', r'"\1"', cleaned)  # unquoted keys

        try:
            result = json.loads(cleaned)
            print(f"✓ Brain Interface Test {i+1}: Successfully parsed")
        except json.JSONDecodeError as e:
            print(f"✗ Brain Interface Test {i+1}: Failed to parse - {e}")

try:
    from main import AccomplishPlugin

    print("=== ACCOMPLISH Plugin Fix Verification ===\n")

    # Test 1: Check if the missing method exists
    plugin = AccomplishPlugin()

    if hasattr(plugin, '_generate_simplified_prompt'):
        print("✓ _generate_simplified_prompt method exists")

        # Test calling the method
        try:
            result = plugin._generate_simplified_prompt(
                "Test goal",
                "Test plugins",
                "Test context"
            )
            print("✓ _generate_simplified_prompt method can be called")
            print(f"  Generated prompt length: {len(result)} characters")
            print(f"  Contains JSON instructions: {'JSON' in result}")
        except Exception as e:
            print(f"✗ Error calling _generate_simplified_prompt: {e}")
    else:
        print("✗ _generate_simplified_prompt method is missing")

    # Test 2: Check ACCOMPLISH-specific JSON parsing
    test_responses = [
        '{"type": "PLAN", "plan": []}',
        '{"number": 1, "actionVerb": "TEST", "description": "test"}',  # single step
        '"plan": [{"number": 1, "actionVerb": "TEST"}]',  # plan fragment
    ]

    print("\nTesting ACCOMPLISH-specific JSON parsing:")
    for i, response in enumerate(test_responses):
        try:
            result = plugin._parse_llm_response(response)
            if result is not None:
                print(f"✓ ACCOMPLISH Test {i+1}: Successfully parsed - type: {type(result).__name__}")
            else:
                print(f"✗ ACCOMPLISH Test {i+1}: Failed to parse response")
        except Exception as e:
            print(f"✗ ACCOMPLISH Test {i+1}: Exception during parsing: {e}")

    print("\n" + "="*50)
    test_brain_interface_json_parsing()

    print("\n✓ All tests completed successfully")
    print("\nSUMMARY:")
    print("- Generic JSON malformation resolution moved to ModelInterface")
    print("- ACCOMPLISH-specific schema compliance remains in main.py")
    print("- Missing method error fixed")
    print("- Enhanced error classification implemented")

except ImportError as e:
    print(f"✗ Failed to import AccomplishPlugin: {e}")
except Exception as e:
    print(f"✗ Unexpected error: {e}")
