2025-07-14 14:50:09.582 | RSA private key for plugin signing not found (this is normal for most services)
2025-07-14 14:50:09.592 | Loaded RSA public key for plugin verification
2025-07-14 14:50:09.648 | Attempting to connect to RabbitMQ (attempt 1/20)...
2025-07-14 14:50:09.648 | Using RabbitMQ URL: amqp://stage7:stage7password@rabbitmq:5672
2025-07-14 14:50:09.648 | Attempting to connect to RabbitMQ host: rabbitmq
2025-07-14 14:50:09.650 | Connecting to RabbitMQ at amqp://stage7:stage7password@rabbitmq:5672
2025-07-14 14:50:09.653 | Attempting to register with <PERSON> (attempt 1/10)...
2025-07-14 14:50:09.654 | Using Consul URL: consul:8500
2025-07-14 14:50:09.750 | Brain service listening at http://0.0.0.0:5070
2025-07-14 14:50:09.759 | [shouldBypassAuth] Bypassing auth for auth path: /v1/agent/service/register (matched /register)
2025-07-14 14:50:09.816 | Loaded interface: anthropic
2025-07-14 14:50:09.842 | Loaded interface: gemini
2025-07-14 14:50:10.422 | Loaded interface: groq
2025-07-14 14:50:10.433 | Loaded interface: huggingface
2025-07-14 14:50:10.438 | Loaded interface: mistral
2025-07-14 14:50:10.441 | Loaded interface: openai
2025-07-14 14:50:10.451 | Loaded interface: openrouter
2025-07-14 14:50:10.461 | OpenWebUIInterface initialized with DEFAULT_TIMEOUT: 300000ms
2025-07-14 14:50:10.461 | Loaded interface: openwebui
2025-07-14 14:50:10.461 | modelManager Loaded 8 interfaces.
2025-07-14 14:50:10.461 | Anthropic Service created, ApiKey starts sk-ant
2025-07-14 14:50:10.461 | Loaded service: AntService
2025-07-14 14:50:10.461 | GG Gemini Service created, ApiKey starts AIzaSy
2025-07-14 14:50:10.461 | Loaded service: GGService
2025-07-14 14:50:10.461 | Gemini Service created, ApiKey starts AIzaSy
2025-07-14 14:50:10.461 | Loaded service: gemini
2025-07-14 14:50:10.461 | Groq Service created, ApiKey starts gsk_m0
2025-07-14 14:50:10.461 | GroqService initialized with API key: Set (length: 56)
2025-07-14 14:50:10.461 | Loaded service: groq
2025-07-14 14:50:10.472 | Huggingface Service created with API key: Set (length: 37)
2025-07-14 14:50:10.472 | Loaded service: HFService
2025-07-14 14:50:10.472 | Mistral Service created, ApiKey starts AhDwC8
2025-07-14 14:50:10.472 | Loaded service: MistralService
2025-07-14 14:50:10.483 | OpenAI Service created, ApiKey starts sk-LaE
2025-07-14 14:50:10.483 | Loaded service: OAService
2025-07-14 14:50:10.485 | OpenRouter Service created, ApiKey starts sk-or-
2025-07-14 14:50:10.485 | Loaded service: ORService
2025-07-14 14:50:10.487 | Openweb Service created, ApiKey starts eyJhbG
2025-07-14 14:50:10.487 | Using default OpenWebUI URL: https://knllm.dusdusdusd.com
2025-07-14 14:50:10.487 | Loaded service: OWService
2025-07-14 14:50:10.494 | modelManager Loaded 9 services.
2025-07-14 14:50:10.503 | Loaded model: hf/meta-llama/llama-3.2-3b-instruct
2025-07-14 14:50:10.517 | Loaded model: suno/bark
2025-07-14 14:50:10.519 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-14 14:50:10.521 | Loaded model: anthropic/claude-3-haiku-20240307
2025-07-14 14:50:10.532 | Loaded model: anthropic/claude-2
2025-07-14 14:50:10.544 | Loaded model: codellama/CodeLlama-34b-Instruct-hf
2025-07-14 14:50:10.544 | Loaded model: THUDM/cogvlm-chat-hf
2025-07-14 14:50:10.555 | Loaded model: openai/dall-e-2
2025-07-14 14:50:10.557 | Loaded model: openai/dall-e-3
2025-07-14 14:50:10.560 | Loaded model: deepseek-ai/DeepSeek-R1
2025-07-14 14:50:11.299 | Loaded model: openai/whisper-large-v3
2025-07-14 14:50:11.311 | Loaded model: google/gemini-2.0-flash-lite
2025-07-14 14:50:11.321 | Loaded model: google/gemini-2.5-flash
2025-07-14 14:50:11.331 | Loaded model: google/gemma-3-27b-it
2025-07-14 14:50:11.333 | Loaded model: openai/gpt-4.1-nano
2025-07-14 14:50:11.335 | Loaded model: openai/gpt-4-vision-preview
2025-07-14 14:50:11.339 | Loaded model: nousresearch/hermes-3-llama-3.1-405b
2025-07-14 14:50:11.340 | KNLLMModel initialized with OpenWebUI interface
2025-07-14 14:50:11.341 | Loaded model: openweb/knownow
2025-07-14 14:50:11.342 | Loaded model: liquid/lfm-40b
2025-07-14 14:50:11.349 | Loaded model: meta-llama/llama-3.2-11b-vision-instruct
2025-07-14 14:50:11.356 | GroqService availability check: Available
2025-07-14 14:50:11.357 | GroqService API key: Set (length: 56)
2025-07-14 14:50:11.357 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-14 14:50:11.357 | GroqService ready state: Ready
2025-07-14 14:50:11.357 | GroqService is available and ready to use.
2025-07-14 14:50:11.357 | Loaded model: groq/llama-4
2025-07-14 14:50:11.358 | Loaded model: meta-llama/Llama-2-70b-chat-hf
2025-07-14 14:50:11.365 | Loaded model: liuhaotian/llava-v1.5-13b
2025-07-14 14:50:11.365 | Loaded model: microsoft/Phi-3.5-vision-instruct
2025-07-14 14:50:11.372 | MistralService availability check: Available
2025-07-14 14:50:11.372 | MistralService API key: Set
2025-07-14 14:50:11.372 | MistralService API URL: https://api.mistral.ai/v1
2025-07-14 14:50:11.372 | MistralService is available and ready to use.
2025-07-14 14:50:11.372 | Loaded model: mistral/mistral-small-latest
2025-07-14 14:50:11.372 | Loaded model: mistralai/Mistral-Nemo-Instruct-2407
2025-07-14 14:50:11.373 | Loaded model: facebook/musicgen-large
2025-07-14 14:50:11.376 | MistralService availability check: Available
2025-07-14 14:50:11.376 | MistralService API key: Set
2025-07-14 14:50:11.377 | MistralService API URL: https://api.mistral.ai/v1
2025-07-14 14:50:11.377 | MistralService is available and ready to use.
2025-07-14 14:50:11.377 | Loaded model: mistral/pixtral-12B-2409
2025-07-14 14:50:11.379 | GroqService availability check: Available
2025-07-14 14:50:11.379 | GroqService API key: Set (length: 56)
2025-07-14 14:50:11.379 | GroqService API URL: https://api.groq.com/openai/v1
2025-07-14 14:50:11.379 | GroqService ready state: Ready
2025-07-14 14:50:11.379 | GroqService is available and ready to use.
2025-07-14 14:50:11.379 | Loaded model: groq/qwen-qwq-32b
2025-07-14 14:50:11.381 | Loaded model: facebook/seamless-m4t-large
2025-07-14 14:50:11.384 | Loaded model: stabilityai/stable-diffusion-xl-base-1.0
2025-07-14 14:50:11.384 | Loaded model: bigcode/starcoder
2025-07-14 14:50:11.388 | Loaded model: openai/tts
2025-07-14 14:50:11.389 | Loaded model: openai/whisper-large-v3
2025-07-14 14:50:11.391 | Loaded model: openai/whisper
2025-07-14 14:50:11.391 | modelManager Loaded 33 models.
2025-07-14 14:50:13.962 | [shouldBypassAuth] Bypassing auth for auth path: /registerComponent (matched /register)
2025-07-14 14:50:14.012 | Service Brain registered with Consul
2025-07-14 14:50:14.014 | Successfully registered Brain with Consul
2025-07-14 14:50:14.032 | [Brain] Attempting to restore model performance data from Librarian...
2025-07-14 14:50:14.058 | [AuthenticatedAxios] Request q6j4qir7og: Failed after 14ms: {
2025-07-14 14:50:14.058 |   status: undefined,
2025-07-14 14:50:14.058 |   statusText: undefined,
2025-07-14 14:50:14.058 |   data: undefined,
2025-07-14 14:50:14.058 |   url: 'http://librarian:5040/loadData/model-performance-data'
2025-07-14 14:50:14.058 | }
2025-07-14 14:50:14.063 | Brain registered successfully with PostOffice
2025-07-14 14:50:15.439 | [Brain] Restored 33 model performance records from Librarian
2025-07-14 14:50:17.655 | Created ServiceTokenManager for Brain
2025-07-14 14:50:23.978 | Connected to RabbitMQ
2025-07-14 14:50:23.989 | Channel created successfully
2025-07-14 14:50:23.989 | RabbitMQ channel ready
2025-07-14 14:50:24.049 | Connection test successful - RabbitMQ connection is stable
2025-07-14 14:50:24.049 | Creating queue: brain-Brain
2025-07-14 14:50:24.058 | Binding queue to exchange: stage7
2025-07-14 14:50:24.067 | Successfully connected to RabbitMQ and set up queues/bindings
2025-07-14 14:52:08.552 | Chat request received
2025-07-14 14:52:08.552 | Selecting model for optimization: accuracy, conversationType: text/code
2025-07-14 14:52:08.552 | **** CACHE MISS **** No cached result for key: accuracy-text/code
2025-07-14 14:52:08.552 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.552 | Total models loaded: 33
2025-07-14 14:52:08.552 | No available models found for conversation type text/code
2025-07-14 14:52:08.552 | No model found for accuracy/text/code, trying next combination...
2025-07-14 14:52:08.552 | Selecting model for optimization: accuracy, conversationType: text/code
2025-07-14 14:52:08.552 | **** CACHE MISS **** No cached result for key: accuracy-text/code
2025-07-14 14:52:08.552 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.552 | Total models loaded: 33
2025-07-14 14:52:08.553 | No available models found for conversation type text/code
2025-07-14 14:52:08.553 | No model found for accuracy/text/code, trying next combination...
2025-07-14 14:52:08.553 | Selecting model for optimization: accuracy, conversationType: text/code
2025-07-14 14:52:08.553 | **** CACHE MISS **** No cached result for key: accuracy-text/code
2025-07-14 14:52:08.553 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.553 | Total models loaded: 33
2025-07-14 14:52:08.553 | No available models found for conversation type text/code
2025-07-14 14:52:08.553 | No model found for accuracy/text/code, trying next combination...
2025-07-14 14:52:08.553 | Selecting model for optimization: cost, conversationType: text/code
2025-07-14 14:52:08.553 | **** CACHE MISS **** No cached result for key: cost-text/code
2025-07-14 14:52:08.553 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.553 | Total models loaded: 33
2025-07-14 14:52:08.553 | No available models found for conversation type text/code
2025-07-14 14:52:08.553 | No model found for cost/text/code, trying next combination...
2025-07-14 14:52:08.553 | Selecting model for optimization: accuracy, conversationType: text/text
2025-07-14 14:52:08.553 | **** CACHE MISS **** No cached result for key: accuracy-text/text
2025-07-14 14:52:08.553 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.553 | Total models loaded: 33
2025-07-14 14:52:08.553 | No available models found for conversation type text/text
2025-07-14 14:52:08.553 | No model found for accuracy/text/text, trying next combination...
2025-07-14 14:52:08.553 | Selecting model for optimization: speed, conversationType: text/code
2025-07-14 14:52:08.553 | **** CACHE MISS **** No cached result for key: speed-text/code
2025-07-14 14:52:08.553 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.553 | Total models loaded: 33
2025-07-14 14:52:08.554 | No available models found for conversation type text/code
2025-07-14 14:52:08.554 | No model found for speed/text/code, trying next combination...
2025-07-14 14:52:08.554 | Selecting model for optimization: creativity, conversationType: text/code
2025-07-14 14:52:08.554 | **** CACHE MISS **** No cached result for key: creativity-text/code
2025-07-14 14:52:08.554 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.554 | Total models loaded: 33
2025-07-14 14:52:08.554 | No available models found for conversation type text/code
2025-07-14 14:52:08.554 | No model found for creativity/text/code, trying next combination...
2025-07-14 14:52:08.554 | Selecting model for optimization: cost, conversationType: text/text
2025-07-14 14:52:08.554 | **** CACHE MISS **** No cached result for key: cost-text/text
2025-07-14 14:52:08.554 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.554 | Total models loaded: 33
2025-07-14 14:52:08.554 | No available models found for conversation type text/text
2025-07-14 14:52:08.554 | No model found for cost/text/text, trying next combination...
2025-07-14 14:52:08.554 | [Brain Chat] All 8 attempts failed. Last error: null
2025-07-14 14:52:08.560 | Chat request received
2025-07-14 14:52:08.560 | Selecting model for optimization: speed, conversationType: text/text
2025-07-14 14:52:08.560 | **** CACHE MISS **** No cached result for key: speed-text/text
2025-07-14 14:52:08.560 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.560 | Total models loaded: 33
2025-07-14 14:52:08.560 | No available models found for conversation type text/text
2025-07-14 14:52:08.560 | No model found for speed/text/text, trying next combination...
2025-07-14 14:52:08.560 | Selecting model for optimization: speed, conversationType: text/text
2025-07-14 14:52:08.560 | **** CACHE MISS **** No cached result for key: speed-text/text
2025-07-14 14:52:08.560 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.560 | Total models loaded: 33
2025-07-14 14:52:08.560 | No available models found for conversation type text/text
2025-07-14 14:52:08.560 | No model found for speed/text/text, trying next combination...
2025-07-14 14:52:08.560 | Selecting model for optimization: speed, conversationType: text/code
2025-07-14 14:52:08.560 | **** CACHE MISS **** No cached result for key: speed-text/code
2025-07-14 14:52:08.560 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.560 | Total models loaded: 33
2025-07-14 14:52:08.560 | No available models found for conversation type text/code
2025-07-14 14:52:08.560 | No model found for speed/text/code, trying next combination...
2025-07-14 14:52:08.560 | Selecting model for optimization: cost, conversationType: text/text
2025-07-14 14:52:08.560 | **** CACHE MISS **** No cached result for key: cost-text/text
2025-07-14 14:52:08.560 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.561 | Total models loaded: 33
2025-07-14 14:52:08.561 | No available models found for conversation type text/text
2025-07-14 14:52:08.561 | No model found for cost/text/text, trying next combination...
2025-07-14 14:52:08.561 | Selecting model for optimization: accuracy, conversationType: text/text
2025-07-14 14:52:08.561 | **** CACHE MISS **** No cached result for key: accuracy-text/text
2025-07-14 14:52:08.561 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.561 | Total models loaded: 33
2025-07-14 14:52:08.561 | No available models found for conversation type text/text
2025-07-14 14:52:08.561 | No model found for accuracy/text/text, trying next combination...
2025-07-14 14:52:08.561 | Selecting model for optimization: speed, conversationType: text/code
2025-07-14 14:52:08.561 | **** CACHE MISS **** No cached result for key: speed-text/code
2025-07-14 14:52:08.561 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.561 | Total models loaded: 33
2025-07-14 14:52:08.561 | No available models found for conversation type text/code
2025-07-14 14:52:08.561 | No model found for speed/text/code, trying next combination...
2025-07-14 14:52:08.561 | Selecting model for optimization: creativity, conversationType: text/text
2025-07-14 14:52:08.561 | **** CACHE MISS **** No cached result for key: creativity-text/text
2025-07-14 14:52:08.561 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.561 | Total models loaded: 33
2025-07-14 14:52:08.561 | No available models found for conversation type text/text
2025-07-14 14:52:08.561 | No model found for creativity/text/text, trying next combination...
2025-07-14 14:52:08.561 | Selecting model for optimization: cost, conversationType: text/text
2025-07-14 14:52:08.561 | **** CACHE MISS **** No cached result for key: cost-text/text
2025-07-14 14:52:08.561 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.561 | Total models loaded: 33
2025-07-14 14:52:08.561 | No available models found for conversation type text/text
2025-07-14 14:52:08.561 | No model found for cost/text/text, trying next combination...
2025-07-14 14:52:08.561 | [Brain Chat] All 8 attempts failed. Last error: null
2025-07-14 14:52:08.566 | Chat request received
2025-07-14 14:52:08.566 | Selecting model for optimization: accuracy, conversationType: text/code
2025-07-14 14:52:08.566 | **** CACHE MISS **** No cached result for key: accuracy-text/code
2025-07-14 14:52:08.566 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.566 | Total models loaded: 33
2025-07-14 14:52:08.566 | No available models found for conversation type text/code
2025-07-14 14:52:08.566 | No model found for accuracy/text/code, trying next combination...
2025-07-14 14:52:08.566 | Selecting model for optimization: accuracy, conversationType: text/code
2025-07-14 14:52:08.566 | **** CACHE MISS **** No cached result for key: accuracy-text/code
2025-07-14 14:52:08.566 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.566 | Total models loaded: 33
2025-07-14 14:52:08.566 | No available models found for conversation type text/code
2025-07-14 14:52:08.566 | No model found for accuracy/text/code, trying next combination...
2025-07-14 14:52:08.566 | Selecting model for optimization: accuracy, conversationType: text/code
2025-07-14 14:52:08.567 | **** CACHE MISS **** No cached result for key: accuracy-text/code
2025-07-14 14:52:08.567 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.567 | Total models loaded: 33
2025-07-14 14:52:08.567 | No available models found for conversation type text/code
2025-07-14 14:52:08.567 | No model found for accuracy/text/code, trying next combination...
2025-07-14 14:52:08.567 | Selecting model for optimization: cost, conversationType: text/code
2025-07-14 14:52:08.567 | **** CACHE MISS **** No cached result for key: cost-text/code
2025-07-14 14:52:08.567 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.567 | Total models loaded: 33
2025-07-14 14:52:08.567 | No available models found for conversation type text/code
2025-07-14 14:52:08.567 | No model found for cost/text/code, trying next combination...
2025-07-14 14:52:08.567 | Selecting model for optimization: accuracy, conversationType: text/text
2025-07-14 14:52:08.567 | **** CACHE MISS **** No cached result for key: accuracy-text/text
2025-07-14 14:52:08.567 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.567 | Total models loaded: 33
2025-07-14 14:52:08.567 | No available models found for conversation type text/text
2025-07-14 14:52:08.567 | No model found for accuracy/text/text, trying next combination...
2025-07-14 14:52:08.567 | Selecting model for optimization: speed, conversationType: text/code
2025-07-14 14:52:08.567 | **** CACHE MISS **** No cached result for key: speed-text/code
2025-07-14 14:52:08.567 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.567 | Total models loaded: 33
2025-07-14 14:52:08.567 | No available models found for conversation type text/code
2025-07-14 14:52:08.567 | No model found for speed/text/code, trying next combination...
2025-07-14 14:52:08.567 | Selecting model for optimization: creativity, conversationType: text/code
2025-07-14 14:52:08.567 | **** CACHE MISS **** No cached result for key: creativity-text/code
2025-07-14 14:52:08.567 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.567 | Total models loaded: 33
2025-07-14 14:52:08.567 | No available models found for conversation type text/code
2025-07-14 14:52:08.567 | No model found for creativity/text/code, trying next combination...
2025-07-14 14:52:08.567 | Selecting model for optimization: cost, conversationType: text/text
2025-07-14 14:52:08.567 | **** CACHE MISS **** No cached result for key: cost-text/text
2025-07-14 14:52:08.568 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.568 | Total models loaded: 33
2025-07-14 14:52:08.568 | No available models found for conversation type text/text
2025-07-14 14:52:08.568 | No model found for cost/text/text, trying next combination...
2025-07-14 14:52:08.568 | [Brain Chat] All 8 attempts failed. Last error: null
2025-07-14 14:52:08.572 | Chat request received
2025-07-14 14:52:08.572 | Selecting model for optimization: speed, conversationType: text/text
2025-07-14 14:52:08.572 | **** CACHE MISS **** No cached result for key: speed-text/text
2025-07-14 14:52:08.572 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.572 | Total models loaded: 33
2025-07-14 14:52:08.572 | No available models found for conversation type text/text
2025-07-14 14:52:08.572 | No model found for speed/text/text, trying next combination...
2025-07-14 14:52:08.572 | Selecting model for optimization: speed, conversationType: text/text
2025-07-14 14:52:08.572 | **** CACHE MISS **** No cached result for key: speed-text/text
2025-07-14 14:52:08.572 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/text
2025-07-14 14:52:08.573 | No model found for speed/text/text, trying next combination...
2025-07-14 14:52:08.573 | Selecting model for optimization: speed, conversationType: text/code
2025-07-14 14:52:08.573 | **** CACHE MISS **** No cached result for key: speed-text/code
2025-07-14 14:52:08.573 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/code
2025-07-14 14:52:08.573 | No model found for speed/text/code, trying next combination...
2025-07-14 14:52:08.573 | Selecting model for optimization: cost, conversationType: text/text
2025-07-14 14:52:08.573 | **** CACHE MISS **** No cached result for key: cost-text/text
2025-07-14 14:52:08.573 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/text
2025-07-14 14:52:08.573 | No model found for cost/text/text, trying next combination...
2025-07-14 14:52:08.573 | Selecting model for optimization: accuracy, conversationType: text/text
2025-07-14 14:52:08.573 | **** CACHE MISS **** No cached result for key: accuracy-text/text
2025-07-14 14:52:08.573 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/text
2025-07-14 14:52:08.573 | No model found for accuracy/text/text, trying next combination...
2025-07-14 14:52:08.573 | Selecting model for optimization: speed, conversationType: text/code
2025-07-14 14:52:08.573 | **** CACHE MISS **** No cached result for key: speed-text/code
2025-07-14 14:52:08.573 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/code
2025-07-14 14:52:08.573 | No model found for speed/text/code, trying next combination...
2025-07-14 14:52:08.573 | Selecting model for optimization: creativity, conversationType: text/text
2025-07-14 14:52:08.573 | **** CACHE MISS **** No cached result for key: creativity-text/text
2025-07-14 14:52:08.573 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/text
2025-07-14 14:52:08.573 | No model found for creativity/text/text, trying next combination...
2025-07-14 14:52:08.573 | Selecting model for optimization: cost, conversationType: text/text
2025-07-14 14:52:08.573 | **** CACHE MISS **** No cached result for key: cost-text/text
2025-07-14 14:52:08.573 | Cache miss or expired. Selecting model from scratch.
2025-07-14 14:52:08.573 | Total models loaded: 33
2025-07-14 14:52:08.573 | No available models found for conversation type text/text
2025-07-14 14:52:08.573 | No model found for cost/text/text, trying next combination...
2025-07-14 14:52:08.573 | [Brain Chat] All 8 attempts failed. Last error: null