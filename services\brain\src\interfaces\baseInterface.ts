import { BaseService, ExchangeType } from '../services/baseService';

// Define LLM types directly here since they were previously in this file
export enum LLMConversationType {
    TextToText = 'text-to-text',
    TextToCode = 'text-to-code',
    CodeToText = 'code-to-text',
    TextToImage = 'text-to-image',
    TextToAudio = 'text-to-audio',
    AudioToText = 'audio-to-text',
    ImageToText = 'image-to-text',
    ImageToImage = 'image-to-image'
}

export type ConvertParamsType = {
    service?: BaseService;
    prompt?: string;
    modelName?: string;
    [key: string]: any;
};

/**
 * Base interface for all LLM service interfaces.
 * Provides common functionality for JSON response handling and message trimming.
 * 
 * IMPORTANT: This class should only handle GENERIC JSON malformation issues.
 * Domain-specific schema validation (like PLAN validation) belongs in individual plugins.
 */
export abstract class BaseInterface {
    protected serviceName: string;
    public interfaceName: string; // For backward compatibility
    protected converters: Map<LLMConversationType, any>; // For backward compatibility

    constructor(serviceName: string) {
        this.serviceName = serviceName;
        this.interfaceName = serviceName; // Default to serviceName
        this.converters = new Map(); // Initialize converters map
    }

    abstract chat(service: BaseService, messages: ExchangeType, options: { max_length?: number, temperature?: number }): Promise<string>;

    abstract convert(service: BaseService, conversionType: LLMConversationType, convertParams: ConvertParamsType): Promise<any>;

    /**
     * Helper method to ensure a response is in valid JSON format.
     * Handles generic JSON malformation issues like markdown blocks, trailing commas, etc.
     * Does NOT handle domain-specific schema validation - that belongs in individual plugins.
     * @param response The response from the LLM.
     * @param requireJson Whether JSON is strictly required.
     * @returns The response, possibly converted to valid JSON string.
     */
    public ensureJsonResponse(response: string, requireJson: boolean = false): string {
        if (!requireJson) {
            return response;
        }

        console.log('Original response length:', response.length);
        console.log('First 200 chars:', response.substring(0, 200));

        // Generic JSON cleaning - no domain-specific logic
        let cleanedResponse = this.cleanJsonResponse(response);
        
        // Try to parse the cleaned response
        try {
            const parsed = JSON.parse(cleanedResponse);
            console.log('Response is valid JSON after cleaning.');
            return JSON.stringify(parsed, null, 2);
        } catch (e) {
            console.log('Response is not valid JSON after cleaning. Returning original response.');
            return response;
        }
    }

    /**
     * Generic JSON cleaning - removes markdown blocks, fixes common JSON issues
     * Does NOT handle domain-specific schema validation
     */
    private cleanJsonResponse(response: string): string {
        let cleaned = response.trim();

        // Remove markdown code blocks
        if (cleaned.includes('```')) {
            const patterns = [
                /```json\s*(.*?)\s*```/s,
                /```\s*(.*?)\s*```/s,
                /```json\s*(.*)/s,
                /```\s*(.*)/s
            ];
            
            for (const pattern of patterns) {
                const match = cleaned.match(pattern);
                if (match) {
                    cleaned = match[1].trim();
                    console.log('Extracted content from markdown code block.');
                    break;
                }
            }
            
            // Fallback: remove all ``` markers
            if (cleaned.includes('```')) {
                cleaned = cleaned.replace(/```(?:json)?/gi, '').trim();
                console.log('Removed remaining markdown code block markers.');
            }
        }

        // Apply common JSON fixes
        const commonFixes = [
            // Enhanced trailing comma removal
            (s: string) => s.replace(/,\s*([\]\}])/g, '$1'),
            // Fix single quotes to double quotes (more careful with content)
            (s: string) => s.replace(/'([^']*)':/g, '"$1":'),
            // Remove single-line comments
            (s: string) => s.replace(/\/\/.*$/gm, ''),
            // Remove multi-line comments
            (s: string) => s.replace(/\/\*[\s\S]*?\*\//g, ''),
            // Add quotes to unquoted keys - improved pattern
            (s: string) => s.replace(/(?<!")(\b[a-zA-Z_][a-zA-Z0-9_]*\b)(?=\s*:)/g, '"$1"'),
            // Fix missing commas between key-value pairs
            (s: string) => s.replace(/("\s*:\s*(?:true|false|null|"[^"]*"|-?[\d\.]+(?:e[+-]?\d+)?)\s*)\n(\s*")/gi, '$1,\n$2'),
            // Fix key"":""value typos
            (s: string) => s.replace(/("\s*":)":"\s*([^"]*")/g, '$1"$2"'),
            // Fix unescaped newlines in strings
            (s: string) => s.replace(/\\n(?!(t|r|b|f|\\|'|"))/g, '\n'),
            // Fix missing commas between array elements
            (s: string) => s.replace(/(\})\s*\n\s*(\{)/g, '$1,\n$2'),
            // Fix missing commas between object properties on same line
            (s: string) => s.replace(/("\s*:\s*"(?:[^"\\]|\\.)*"\s+)(")/g, '$1, $2'),
        ];

        const applyFixes = (s: string): string => {
            return commonFixes.reduce((acc, fix) => fix(acc), s);
        };

        cleaned = applyFixes(cleaned);

        // Try to extract JSON from malformed text if direct parsing fails
        try {
            JSON.parse(cleaned);
            return cleaned; // Already valid
        } catch (e) {
            // Try to find JSON object
            const firstBrace = cleaned.indexOf('{');
            const lastBrace = cleaned.lastIndexOf('}');
            if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
                const extracted = cleaned.substring(firstBrace, lastBrace + 1);
                const fixedExtracted = applyFixes(extracted);
                try {
                    JSON.parse(fixedExtracted);
                    console.log('Successfully extracted and fixed JSON object.');
                    return fixedExtracted;
                } catch (e2) {
                    // Continue to array extraction
                }
            }
            
            // Try to find JSON array
            const firstBracket = cleaned.indexOf('[');
            const lastBracket = cleaned.lastIndexOf(']');
            if (firstBracket !== -1 && lastBracket !== -1 && lastBracket > firstBracket) {
                const extracted = cleaned.substring(firstBracket, lastBracket + 1);
                const fixedExtracted = applyFixes(extracted);
                try {
                    JSON.parse(fixedExtracted);
                    console.log('Successfully extracted and fixed JSON array.');
                    return fixedExtracted;
                } catch (e3) {
                    // Fall through
                }
            }
        }

        return cleaned; // Return best effort
    }

    protected trimMessages(messages: ExchangeType, maxTokens: number): ExchangeType {
        const targetTokens = Math.floor(maxTokens / 2);
        let estimatedTokens = 0;
        const trimmedMessages: ExchangeType = [];

        // Estimate tokens (4 characters ~= 1 token)
        const estimateTokens = (text: string) => Math.ceil(text.length / 4);

        // Iterate through messages in reverse order
        for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            let messageTokens = 0;

            if (typeof message.content === 'string') {
                messageTokens = estimateTokens(message.content);
            } else if (Array.isArray((message as any).content)) {
                messageTokens = ((message as any).content as any[]).reduce((sum: number, part: any) => {
                    if (typeof part === 'string') {
                        return sum + estimateTokens(part);
                    } else if (part.type === 'text' && typeof part.text === 'string') {
                        return sum + estimateTokens(part.text);
                    }
                    return sum + 10; // Estimate for non-text content
                }, 0);
            }

            if (estimatedTokens + messageTokens <= targetTokens) {
                trimmedMessages.unshift(message);
                estimatedTokens += messageTokens;
            } else {
                break;
            }
        }

        return trimmedMessages.length > 0 ? trimmedMessages : [messages[messages.length - 1]];
    }
}
